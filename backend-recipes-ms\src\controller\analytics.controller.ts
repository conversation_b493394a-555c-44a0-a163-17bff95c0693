import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import analyticsService from "../services/analytics.service";
import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import Recipe from "../models/Recipe";
import { ValidationHelper } from "../helper/validation.helper";
import { <PERSON>rrorHandler } from "../helper/transaction.helper";

/**
 * Track CTA clicks on public recipes
 * @route POST /api/v1/public/analytics/track/cta-click
 */
const trackCtaClick = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name, cta_type, cta_text } = sanitizedBody;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
        },
      });
    }

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CTA_CLICK,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        cta_type,
        cta_text,
        tracking_source: req.user ? "authenticated" : "public",
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "CTA click tracked successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error tracking CTA click"
    );
  }
};

/**
 * Submit contact form from public recipes
 * @route POST /api/v1/public/analytics/contact-form
 */
const submitContactForm = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name, name, email, mobile, message } =
      sanitizedBody;

    // Validate required fields
    if (!recipe_id || !name || !email) {
      return res.status(400).json({
        status: false,
        message: "recipe_id, name, and email are required",
        missing_fields: {
          recipe_id: !recipe_id,
          name: !name,
          email: !email,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate recipe_id data type
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        status: false,
        message: "Invalid email format",
      });
    }

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.CONTACT_FORM_SUBMIT,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: {
        recipe_name,
        contact_name: name,
        contact_email: email,
        contact_mobile: mobile,
        message,
        tracking_source: req.user ? "authenticated" : "public",
      },
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Contact form submitted successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error submitting contact form"
    );
  }
};

/**
 * Track recipe views on public recipes
 * @route POST /v1/public/analytics/track/recipe-view
 */
const trackRecipeView = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipe_id, recipe_name } = sanitizedBody;

    // Validate required fields
    if (!recipe_id) {
      return res.status(400).json({
        status: false,
        message: "recipe_id is required",
        missing_fields: {
          recipe_id: !recipe_id,
        },
      });
    }

    // Convert recipe_id to number if it's a string
    const recipeIdNumber =
      typeof recipe_id === "string" ? parseInt(recipe_id) : recipe_id;

    // Validate data types
    if (
      typeof recipeIdNumber !== "number" ||
      isNaN(recipeIdNumber) ||
      recipeIdNumber <= 0
    ) {
      return res.status(400).json({
        status: false,
        message: "recipe_id must be a positive number",
        received_data: {
          recipe_id: recipe_id,
          recipe_id_type: typeof recipe_id,
          parsed_recipe_id: recipeIdNumber,
          is_valid_number: !isNaN(recipeIdNumber) && recipeIdNumber > 0,
        },
      });
    }

    // Track recipe view only
    const metadata: any = {
      recipe_name,
      timestamp: new Date().toISOString(),
      tracking_source: req.user ? "authenticated" : "public",
    };

    // Get recipe's organization_id for proper analytics filtering
    const recipe = await Recipe.findByPk(recipeIdNumber, {
      attributes: ["organization_id"],
    });

    // Use existing trackEvent method
    await analyticsService.trackEvent({
      eventType: AnalyticsEventType.RECIPE_VIEW,
      entityType: AnalyticsEntityType.RECIPE,
      entityId: recipeIdNumber,
      organizationId: recipe?.organization_id,
      userId: req.user?.id,
      ipAddress: req.ip,
      userAgent: req.get("User-Agent"),
      metadata: metadata,
    });

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe view tracked successfully",
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error tracking recipe activity"
    );
  }
};

/**
 * Get CTA click analytics with pagination
 * @route GET /api/v1/private/analytics/cta-clicks
 */
const getCtaClickAnalytics = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      time_period, // Support both date_range and time_period
      start_date,
      end_date,
      sort_order = "desc",
      sort_by,
      page,
      limit,
      cta_type,
      recipe_name,
      search,
    } = sanitizedQuery;

    // Use time_period as fallback for date_range, with default
    const finalDateRange = date_range || time_period || "last_30_days";

    // Validate date_range parameter if provided
    if (finalDateRange && finalDateRange !== "all_time") {
      const validDateRanges = [
        "last_7_days",
        "last_30_days",
        "last_90_days",
        "all_time",
        "custom",
      ];
      if (!validDateRanges.includes(finalDateRange)) {
        return res.status(400).json({
          status: false,
          message:
            "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0",
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    const result = await analyticsService.getCtaClickAnalytics(
      effectiveOrganizationId || undefined,
      finalDateRange as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber,
      cta_type,
      recipe_name || search, // Use search as fallback for recipe_name
      sort_order,
      sort_by
    );

    // Sort results if no pagination (pagination already handles sorting)
    if (!pageNumber && !limitNumber) {
      if (sort_order === "asc") {
        result.data.sort((a: any, b: any) => a.clicks - b.clicks);
      } else {
        result.data.sort((a: any, b: any) => b.clicks - a.clicks);
      }
    }

    const response: any = {
      status: true,
      message: res.__("CTA_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: result.data,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: result.total,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching CTA analytics"
    );
  }
};

/**
 * Get contact form submission analytics with pagination
 * @route GET /api/v1/private/analytics/contact-analytics
 */
const getContactSubmissionAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range,
      start_date,
      end_date,
      recipe_id,
      page,
      limit,
      search,
      sort_order,
      sort_by,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate date_range parameter if provided
    if (date_range) {
      const validDateRanges = [
        "last_7_days",
        "last_30_days",
        "last_90_days",
        "custom",
      ];
      if (!validDateRanges.includes(date_range)) {
        return res.status(400).json({
          status: false,
          message:
            "Invalid date_range. Must be one of: " + validDateRanges.join(", "),
        });
      }
    }

    // Parse pagination parameters
    const pageNumber = page ? parseInt(page) : undefined;
    const limitNumber = limit ? parseInt(limit) : undefined;

    // Validate pagination parameters
    if (pageNumber && pageNumber < 1) {
      return res.status(400).json({
        status: false,
        message: "Page number must be greater than 0",
      });
    }

    if (limitNumber && (limitNumber < 1 || limitNumber > 100)) {
      return res.status(400).json({
        status: false,
        message: "Limit must be between 1 and 100",
      });
    }

    const result = await analyticsService.getContactSubmissionAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      pageNumber,
      limitNumber,
      search, // Unified search parameter only
      sort_order,
      sort_by
    );

    // Filter by recipe if specified (only when no pagination to avoid incorrect counts)
    let filteredData = result.data;
    let filteredTotal = result.total;

    if (recipe_id && !pageNumber && !limitNumber) {
      filteredData = result.data.filter(
        (item: any) => item.recipe_id === Number(recipe_id)
      );
      filteredTotal = filteredData.length;
    }

    const response: any = {
      status: true,
      message: res.__("CONTACT_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: filteredData,
      meta: {
        date_range: date_range || null,
        start_date: start_date || null,
        end_date: end_date || null,
        recipe_filter: recipe_id || null,
        total_records: filteredTotal,
      },
    };

    // Add pagination info if pagination was used
    if (result.pagination) {
      response.pagination = result.pagination;
    }

    return res.status(StatusCodes.OK).json(response);
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching contact analytics"
    );
  }
};

/**
 * Get recipe view analytics
 * @route GET /api/v1/private/analytics/recipe-views
 */
const getRecipeViewAnalytics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      date_range = "last_30_days",
      start_date,
      end_date,
      sort_order = "desc",
      sort_by = "view_count",
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    const analytics = await analyticsService.getRecipeViewAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date
    );

    // Sort results by specified field and order
    if (sort_by === "view_count") {
      if (sort_order === "asc") {
        analytics.sort((a: any, b: any) => a.total_views - b.total_views);
      } else {
        analytics.sort((a: any, b: any) => b.total_views - a.total_views);
      }
    } else if (sort_by === "recipe_name") {
      if (sort_order === "asc") {
        analytics.sort((a: any, b: any) =>
          a.recipe_name.localeCompare(b.recipe_name)
        );
      } else {
        analytics.sort((a: any, b: any) =>
          b.recipe_name.localeCompare(a.recipe_name)
        );
      }
    } else if (sort_by === "last_viewed") {
      if (sort_order === "asc") {
        analytics.sort(
          (a: any, b: any) =>
            new Date(a.last_viewed).getTime() -
            new Date(b.last_viewed).getTime()
        );
      } else {
        analytics.sort(
          (a: any, b: any) =>
            new Date(b.last_viewed).getTime() -
            new Date(a.last_viewed).getTime()
        );
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_VIEW_ANALYTICS_FETCHED_SUCCESSFULLY"),
      data: analytics,
      meta: {
        date_range,
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: analytics.length,
        sort_order: sort_order,
        sort_by: sort_by,
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching recipe view analytics"
    );
  }
};

/**
 * Export contact form submissions to CSV or JSON
 * @route GET /api/v1/private/analytics/contact-analytics/export
 */
const exportContactSubmissions = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      format = "csv",
      date_range = "last_30_days",
      start_date,
      end_date,
      recipe_name,
      user_email,
      search,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Validate format
    if (!["csv", "json"].includes(format)) {
      return res.status(400).json({
        status: false,
        message: "Invalid format. Supported formats: csv, json",
      });
    }

    // Get all contact submissions without pagination for export
    const result = await analyticsService.getContactSubmissionAnalytics(
      effectiveOrganizationId || undefined,
      date_range as string,
      start_date,
      end_date,
      undefined, // no pagination
      undefined, // no limit
      recipe_name || search,
      user_email || search
    );

    const timestamp = new Date().toISOString().split("T")[0];

    if (format === "csv") {
      // Convert to CSV format
      const csv = convertContactSubmissionsToCSV(result.data, date_range);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=contact-analytics-export-${timestamp}.csv`
      );
      return res.send(csv);
    }

    // Default JSON export
    res.setHeader("Content-Type", "application/json");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=contact-analytics-export-${timestamp}.json`
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Contact submissions exported successfully",
      data: result.data,
      total: result.total,
      exportedAt: new Date().toISOString(),
      dateRange: date_range,
      organizationId: effectiveOrganizationId,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error exporting contact submissions"
    );
  }
};

/**
 * Delete contact form submission
 * @route DELETE /api/v1/private/analytics/contact-analytics/:id
 */
const deleteContactSubmission = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;

    // Validate ID parameter
    if (!id || isNaN(Number(id))) {
      return res.status(400).json({
        status: false,
        message: "Valid submission ID is required",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(req.user);

    // Build where clause
    const whereClause: any = {
      id: Number(id),
      event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
    };

    // Add organization filter if not admin
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    const deleted = await Analytics.destroy({
      where: whereClause,
    });

    if (!deleted) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Contact submission not found",
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CONTACT_SUBMISSION_DELETED_SUCCESSFULLY"),
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting contact submission"
    );
  }
};

/**
 * Bulk delete contact form submissions from analytics
 * @route DELETE /api/v1/private/analytics/contact-analytics/bulk-delete
 */
const bulkDeleteContactSubmissions = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);
    const { submission_ids, delete_all = false, filters = {} } = sanitizedBody;

    // Validate input
    if (
      !delete_all &&
      (!submission_ids ||
        !Array.isArray(submission_ids) ||
        submission_ids.length === 0)
    ) {
      return res.status(400).json({
        status: false,
        message:
          "Please provide submission_ids array or set delete_all to true with filters.",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(req.user);

    let deletedCount = 0;
    const whereClause: any = {
      event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
    };

    // Add organization filter if not admin
    if (effectiveOrganizationId !== undefined) {
      whereClause.organization_id = effectiveOrganizationId;
    }

    if (delete_all) {
      // Build where clause from filters
      const { search, recipe_name, user_email, start_date, end_date } = filters;

      // Apply date range filter
      if (start_date || end_date) {
        const dateFilter: any = {};
        if (start_date) {
          dateFilter[Op.gte] = new Date(start_date as string);
        }
        if (end_date) {
          const endDateTime = new Date(end_date as string);
          endDateTime.setHours(23, 59, 59, 999); // End of day
          dateFilter[Op.lte] = endDateTime;
        }
        whereClause.created_at = dateFilter;
      }

      // Apply search filters using metadata JSON queries
      if (search && typeof search === "string" && search.trim()) {
        const searchTerm = search.trim();
        whereClause[Op.or] = [
          { "$metadata.contact_name$": { [Op.iLike]: `%${searchTerm}%` } },
          { "$metadata.contact_email$": { [Op.iLike]: `%${searchTerm}%` } },
          { "$metadata.recipe_name$": { [Op.iLike]: `%${searchTerm}%` } },
        ];
      }

      if (
        recipe_name &&
        typeof recipe_name === "string" &&
        recipe_name.trim()
      ) {
        whereClause["$metadata.recipe_name$"] = {
          [Op.iLike]: `%${recipe_name.trim()}%`,
        };
      }

      if (user_email && typeof user_email === "string" && user_email.trim()) {
        whereClause["$metadata.contact_email$"] = {
          [Op.iLike]: `%${user_email.trim()}%`,
        };
      }

      // Delete all matching records
      deletedCount = await Analytics.destroy({
        where: whereClause,
      });
    } else {
      // Validate submission IDs
      const validIds = submission_ids.filter((id: any) => {
        const numId = parseInt(id, 10);
        return !isNaN(numId) && numId > 0;
      });

      if (validIds.length === 0) {
        return res.status(400).json({
          status: false,
          message: "No valid submission IDs provided.",
        });
      }

      // Delete specific submissions
      whereClause.id = { [Op.in]: validIds };
      deletedCount = await Analytics.destroy({
        where: whereClause,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Successfully deleted ${deletedCount} contact form submission(s) from analytics.`,
      data: {
        deleted_count: deletedCount,
        operation: delete_all ? "bulk_delete_filtered" : "bulk_delete_by_ids",
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error bulk deleting contact form submissions"
    );
  }
};

/**
 * Get analytics summary - Real data from database
 * @route GET /api/v1/private/analytics
 */
const getAnalyticsSummary = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);

    const {
      page,
      limit,
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        sanitizedQuery.organization_id
      );

    // Get real analytics data from service
    const summary = await analyticsService.getAnalyticsSummary({
      organizationId: effectiveOrganizationId || undefined,
      page: parseInt(page) || 1,
      limit: Math.min(parseInt(limit) || 10, 50),
      event_type,
      entity_type,
      entity_id,
      start_date,
      end_date,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Analytics summary fetched successfully",
      data: summary,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching analytics summary"
    );
  }
};

/**
 * Get recipe view statistics for private recipes with assigned users
 * @route GET /api/v1/private/analytics/recipe-view-statistics/:recipeId
 */
const getRecipeViewStatistics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedParams = ValidationHelper.sanitizeInput(req.params);
    const { recipeId } = sanitizedParams;

    // Convert recipeId to number
    const recipeIdNumber = parseInt(recipeId);
    if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "Invalid recipe ID. Must be a positive number",
      });
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        req.query.organization_id
      );

    // Call service method
    const result = await analyticsService.getRecipeViewStatistics(
      recipeIdNumber,
      effectiveOrganizationId || undefined
    );
    // Return response
    if (!result.status) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: result.message,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: result.message,
      data: result.data,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching recipe view statistics"
    );
  }
};

/**
 * Reset recipe view statistics for private recipes with assigned users
 * @route DELETE /api/v1/private/analytics/reset-view-statistics/:recipeId
 */
const resetRecipeViewStatistics = async (
  req: any,
  res: Response
): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedParams = ValidationHelper.sanitizeInput(req.params);
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const { recipeId } = sanitizedParams;
    const { user_ids } = sanitizedBody;

    // Convert recipeId to number
    const recipeIdNumber = parseInt(recipeId);
    if (isNaN(recipeIdNumber) || recipeIdNumber <= 0) {
      return res.status(400).json({
        status: false,
        message: "Invalid recipe ID. Must be a positive number",
      });
    }

    // Validate user_ids if provided
    let userIds: number[] | undefined;
    if (user_ids) {
      if (!Array.isArray(user_ids)) {
        return res.status(400).json({
          status: false,
          message: "user_ids must be an array of user IDs",
        });
      }

      try {
        userIds = user_ids.map((id: any) => {
          const userId = parseInt(id);
          if (isNaN(userId) || userId <= 0) {
            throw new Error(
              `Invalid user ID: ${id}. Must be a positive number`
            );
          }
          return userId;
        });

        if (userIds.length === 0) {
          return res.status(400).json({
            status: false,
            message: "user_ids array cannot be empty when provided",
          });
        }
      } catch (error: any) {
        return res.status(400).json({
          status: false,
          message: error.message,
        });
      }
    }

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId =
      await ValidationHelper.getEffectiveOrganizationId(
        req.user,
        req.query.organization_id
      );

    // Call service method
    const result = await analyticsService.resetRecipeViewStatistics(
      recipeIdNumber,
      effectiveOrganizationId || undefined,
      userIds
    );
    if (!result.status) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: result.message,
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: result.message,
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error resetting recipe view statistics"
    );
  }
};

// Helper function to convert contact submissions to CSV
function convertContactSubmissionsToCSV(
  data: any[],
  dateRange: string = "last_30_days"
): string {
  const headers = [
    "Recipe ID",
    "Recipe Name",
    "Contact Name",
    "Email",
    "Mobile",
    "Message",
    "Submitted At",
    "Days Ago",
  ];

  const rows = data.map((item) => [
    item.recipe_id || "N/A",
    item.recipe_name || "Unknown Recipe",
    item.contact_name || "N/A",
    item.contact_email || "N/A",
    item.contact_mobile || "N/A",
    (item.message || "").replace(/"/g, '""').replace(/\n/g, " "), // Escape quotes and newlines
    item.submitted_at || "N/A",
    item.time_ago || "N/A",
  ]);

  const csvContent = [
    `# Contact Form Submissions Export - ${new Date().toISOString()}`,
    `# Date Range: ${dateRange}`,
    `# Generated by Recipe Management System`,
    "",
    headers.join(","),
    ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  return csvContent;
}

export default {
  // Public analytics (simplified)
  trackCtaClick,
  submitContactForm,
  trackRecipeView,

  // Dashboard analytics (simplified)
  getCtaClickAnalytics,
  getContactSubmissionAnalytics,
  exportContactSubmissions,
  deleteContactSubmission,
  bulkDeleteContactSubmissions,

  // Recipe view statistics endpoints
  getRecipeViewStatistics,
  resetRecipeViewStatistics,
};
