{"info": {"_postman_id": "39b788a8-a8ab-47c6-8b45-83cef885fce6", "name": "🎫 Customer Service Microservice - Complete API Collection", "description": "🎫 **Customer Service Microservice - Complete API Collection**\n\n**Features:**\n- ✅ Support ticket management with PIN verification\n- ✅ File attachments (up to 5MB per file)\n- ✅ Real-time conversation system\n- ✅ AI-powered assignee suggestions\n- ✅ Admin dashboard and bulk operations\n- ✅ Organization-scoped access control\n- ✅ Advanced filtering and search\n\n**Authentication:** Bearer JWT token required for all private endpoints\n\n**Environments:**\n- Development: http://localhost:8030\n- Staging: http://localhost:9030 (connects to staging database)\n\n**Quick Start:**\n1. Set your JWT token in the `auth_token` variable\n2. Update `organization_id` and `support_pin` variables\n3. Test with health check endpoints first\n4. Create tickets using the support PIN\n\n**Note:** Public ticket creation is disabled - all endpoints require authentication.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40766612", "_collection_link": "https://sdadfasdfasdd.postman.co/workspace/My-Workspace~110d6b3d-9f51-4f2e-81ec-1f5290bae170/collection/40766612-39b788a8-a8ab-47c6-8b45-83cef885fce6?action=share&source=collection_link&creator=40766612"}, "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}, {"name": "Public Health Check", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Msxousd7PkyMnalOneI4iFPHWDUF3uDnFiXwtkANwUw", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/public/health", "host": ["{{base_url}}"], "path": ["v1", "public", "health"]}}, "response": []}]}, {"name": "🎫 Support Tickets", "item": [{"name": "Get All Tickets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/list?page=1&limit=10&status=open&priority=high&module_type=hrms&assigned_to_user_id=123&organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, qa_review, under_review, escalated, resolved, closed, invoiced"}, {"key": "priority", "value": "high", "description": "Filter by priority: emergency, urgent, high, medium, low"}, {"key": "module_type", "value": "hrms", "description": "Filter by module: hrms, pms, other"}, {"key": "assigned_to_user_id", "value": "123", "description": "Filter by assigned user ID"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}]}}, "response": []}, {"name": "Create Ticket (Private - Authentication Required)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "submitter_name", "value": "<PERSON>", "description": "Name of ticket submitter (required)", "type": "text"}, {"key": "submitter_email", "value": "<EMAIL>", "description": "Email of ticket submitter (required)", "type": "text"}, {"key": "submitter_phone", "value": "+1234567890", "description": "Phone number (optional)", "type": "text"}, {"key": "subject", "value": "Unable to export recipe data", "description": "Brief subject (required, max 200 chars)", "type": "text"}, {"key": "description", "value": "I'm having trouble exporting my recipe data. The export button doesn't respond when clicked.", "description": "Detailed description (required)", "type": "text"}, {"key": "support_pin", "value": "{{support_pin}}", "description": "Organization support PIN (required)", "type": "text"}, {"key": "issue_type", "value": "export_help", "description": "Issue type: bug, feature_request, general_query, technical, non_technical, export_help, support, other", "type": "text"}, {"key": "priority", "value": "medium", "description": "Priority: emergency, urgent, high, medium, low", "type": "text"}, {"key": "ticketFiles", "description": "File attachments (max 5 files, 5MB each)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/create", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "create"]}}, "response": []}, {"name": "Get Ticket by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}"]}}, "response": []}, {"name": "Assign <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"assigned_to_user_id\": 123,\n  \"change_note\": \"Assigning to technical support specialist\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/assign/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "assign", "{{ticket_id}}"]}}, "response": []}, {"name": "Update Ticket Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"change_note\": \"Started working on the issue\",\n  \"resolution_note\": \"Investigating export functionality\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/status/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "status", "{{ticket_id}}"]}}, "response": []}, {"name": "Update Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "subject", "value": "Updated: Unable to export recipe data", "description": "Updated subject", "type": "text"}, {"key": "description", "value": "Updated description with more details about the export issue.", "description": "Updated description", "type": "text"}, {"key": "priority", "value": "HIGH", "description": "Updated priority: EMERGENCY, URGENT, HIGH, MEDIUM, LOW", "type": "text"}, {"key": "ticketFiles", "description": "Additional file attachments", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/update/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "update", "{{ticket_id}}"]}}, "response": []}, {"name": "Delete Ticket", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/delete/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "delete", "{{ticket_id}}"]}}, "response": []}, {"name": "Resolve Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"Issue resolved by updating export functionality\",\n  \"change_note\": \"Ticket resolved successfully\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/resolve/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "resolve", "{{ticket_id}}"]}}, "response": []}, {"name": "Rate Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"feedback\": \"Excellent support, issue resolved quickly\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/rate/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "rate", "{{ticket_id}}"]}}, "response": []}, {"name": "Update T<PERSON><PERSON> (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Msxousd7PkyMnalOneI4iFPHWDUF3uDnFiXwtkANwUw", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"module_type\": \"RECIPE\",\n  \"change_note\": \"Reassigning to recipe module team\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/module", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "module"]}}, "response": []}]}, {"name": "💬 Ticket Comments & Conversation", "item": [{"name": "Add Comment to Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "Thank you for reporting this issue. We are investigating the export functionality.", "description": "Comment text (required, 1-5000 chars)", "type": "text"}, {"key": "is_private", "value": "false", "description": "Whether this is an internal comment (agents/admins only)", "type": "text"}, {"key": "commentFiles", "description": "Optional file attachment for the comment", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/comments", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "comments"]}}, "response": []}, {"name": "Get Ticket Comments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/comments?include_private=false", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "comments"], "query": [{"key": "include_private", "value": "false", "description": "Include private comments (agents/admins only)"}]}}, "response": []}, {"name": "Get Ticket Conversation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/conversation?page=1&size=20&include_private=false", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "conversation"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "size", "value": "20", "description": "Messages per page (1-50, default: 20)"}, {"key": "include_private", "value": "false", "description": "Include private messages (agents/admins only)"}]}}, "response": []}, {"name": "Send Conversation Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "I've identified the issue with the export functionality. Working on a fix now.", "description": "Message content (required, 1-5000 chars)", "type": "text"}, {"key": "is_private", "value": "false", "description": "Whether this is an internal message (agents/admins only)", "type": "text"}, {"key": "messageFiles", "description": "Optional file attachment for the message", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/conversation", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "conversation"]}}, "response": []}]}, {"name": "📨 Support Messages", "item": [{"name": "Get Ticket Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages?page=1&limit=50", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages"], "query": [{"key": "include_private", "value": "false", "description": "Include private/internal messages"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "50", "description": "Messages per page (1-100, default: 50)"}, {"key": "message_type", "value": "AGENT", "description": "Filter by type: USER, AGENT, SYSTEM, INTERNAL_NOTE"}, {"key": "since", "value": "2024-01-01T00:00:00Z", "description": "Get messages since this timestamp"}]}}, "response": []}, {"name": "Add Message to Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "The export issue has been resolved. Please try exporting your data again.", "description": "Message content (required, 1-10000 chars)", "type": "text"}, {"key": "message_type", "value": "AGENT", "description": "Message type: USER, AGENT, SYSTEM, INTERNAL_NOTE", "type": "text"}, {"key": "is_private", "value": "false", "description": "Whether message is private", "type": "text"}, {"key": "attachment_id", "value": "123", "description": "ID of attached file (from nv_items)", "type": "text"}, {"key": "messageFiles", "description": "File attachments (max 3 files)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/messages", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "messages"]}}, "response": []}]}, {"name": "👥 User Management & AI", "item": [{"name": "Get Assignable Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/assignable-users?search=john&roleFilter=agent&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "assignable-users"], "query": [{"key": "search", "value": "john", "description": "Search term for user name or email"}, {"key": "<PERSON><PERSON><PERSON>er", "value": "agent", "description": "Filter by user role"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20)"}]}}, "response": []}, {"name": "AI Suggest Assignee", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticketData\": {\n    \"description\": \"User is unable to export recipe data from the system\",\n    \"subject\": \"Export functionality not working\",\n    \"priority\": \"HIGH\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/suggest-assignee", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "suggest-assignee"]}}, "response": []}]}, {"name": "🔧 Admin Dashboard & Management", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/dashboard?organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "dashboard"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}]}}, "response": []}, {"name": "Admin Get Tickets with Advanced Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/tickets?page=1&limit=10&search=export&status=OPEN&status=IN_PROGRESS&priority=HIGH&priority=URGENT&module_type=HRMS&issue_type=BUG&assigned_to_user_id=null&organization_id={{organization_id}}&submitter_email=<EMAIL>&date_from=2024-01-01&date_to=2024-12-31&overdue=true&unassigned=false&has_rating=false&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "search", "value": "export", "description": "Search in ticket number, subject, submitter name/email"}, {"key": "status", "value": "OPEN", "description": "Filter by status (can be multiple)"}, {"key": "status", "value": "IN_PROGRESS", "description": "Additional status filter"}, {"key": "priority", "value": "HIGH", "description": "Filter by priority (can be multiple)"}, {"key": "priority", "value": "URGENT", "description": "Additional priority filter"}, {"key": "module_type", "value": "HRMS", "description": "Filter by module: HRMS, PMS, OTHER"}, {"key": "issue_type", "value": "BUG", "description": "Filter by issue type: BUG, FEATURE_REQUEST, QUERY, EXPORT_ISSUE, TECHNICAL_SUPPORT, OTHER"}, {"key": "assigned_to_user_id", "value": "null", "description": "Filter by assigned user (use 'null' for unassigned)"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}, {"key": "submitter_email", "value": "<EMAIL>", "description": "Filter by submitter email"}, {"key": "date_from", "value": "2024-01-01", "description": "Filter tickets created from this date"}, {"key": "date_to", "value": "2024-12-31", "description": "Filter tickets created until this date"}, {"key": "overdue", "value": "true", "description": "Filter overdue tickets only"}, {"key": "unassigned", "value": "false", "description": "Filter unassigned tickets only"}, {"key": "has_rating", "value": "false", "description": "Filter tickets with/without rating"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (default: created_at)"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC (default: DESC)"}]}}, "response": []}, {"name": "Bulk Ticket Operations", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3, 4, 5],\n  \"operation\": \"assign\",\n  \"data\": {\n    \"assigned_to_user_id\": 123,\n    \"change_note\": \"Bulk assignment to technical team\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}, {"name": "Bulk Status Update", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3],\n  \"operation\": \"status_update\",\n  \"data\": {\n    \"status\": \"RESOLVED\",\n    \"change_note\": \"Bulk resolution after system update\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}, {"name": "Admin Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/analytics?organization_id={{organization_id}}&period=30d&date_from=2024-01-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "analytics"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)", "disabled": true}, {"key": "period", "value": "30d", "description": "Time period: 7d, 30d, 90d, 1y (default: 30d)"}, {"key": "date_from", "value": "2024-01-01", "description": "Custom start date (overrides period)", "disabled": true}, {"key": "date_to", "value": "2024-12-31", "description": "Custom end date (overrides period)", "disabled": true}]}}, "response": []}, {"name": "Bulk Priority Update", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3],\n  \"operation\": \"priority_update\",\n  \"data\": {\n    \"priority\": \"HIGH\",\n    \"change_note\": \"Escalating priority due to business impact\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}]}, {"name": "⚙️ Support Configuration", "item": [{"name": "Get All Support Configurations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}]}}, "response": []}, {"name": "Get Organization Support Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "config", "{{organization_id}}"]}}, "response": []}, {"name": "Create/Update Support Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"support_pin\": \"SUPPORT2024\",\n  \"is_active\": true,\n  \"allow_attachments\": true,\n  \"max_attachment_size\": 5242880,\n  \"allowed_file_types\": [\"pdf\", \"png\", \"jpg\", \"jpeg\", \"doc\", \"docx\"],\n  \"auto_assignment_enabled\": false\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "config", "{{organization_id}}"]}}, "response": []}, {"name": "Delete Support Config", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "config", "{{organization_id}}"]}}, "response": []}, {"name": "Toggle Support Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/config/{{organization_id}}/toggle", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "config", "{{organization_id}}", "toggle"]}}, "response": []}, {"name": "Test Configuration", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"test_type\": \"pin_validation\",\n  \"test_data\": {\n    \"support_pin\": \"SUPPORT2024\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/config/{{organization_id}}/test", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "config", "{{organization_id}}", "test"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}], "variable": [{"key": "base_url", "value": "http://localhost:9030"}, {"key": "auth_token", "value": "your_jwt_token_here"}, {"key": "ticket_id", "value": "1"}, {"key": "organization_id", "value": "org-001"}, {"key": "support_pin", "value": "SUPPORT2024"}, {"key": "user_id", "value": "123"}, {"key": "staging_base_url", "value": "http://localhost:9030"}, {"key": "development_base_url", "value": "http://localhost:8030"}]}